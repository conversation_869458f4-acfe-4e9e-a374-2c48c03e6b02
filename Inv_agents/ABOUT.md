# Inv_agents Directory - Comprehensive Documentation

## Overview

The `Inv_agents` directory contains an advanced AI-powered vulnerability assessment framework designed for automated penetration testing. This system leverages specialized AI agents to detect and exploit web application vulnerabilities, specifically focusing on SQL Injection (SQLi) and Cross-Site Scripting (XSS) attacks.

## Purpose and Mission

This framework serves as an autonomous penetration testing solution that:
- **Automates vulnerability discovery** using AI-driven analysis
- **Performs intelligent exploitation** with context-aware payload generation
- **Provides comprehensive reporting** with detailed evidence and reproduction steps
- **Operates in isolated environments** for safe testing
- **Supports multiple AI models** for diverse testing approaches

## Directory Structure

```
Inv_agents/
├── sqliAgent.py              # SQL Injection detection and exploitation agent
├── xssAgent.py               # Cross-Site Scripting detection and exploitation agent
├── run_sqli.sh               # SQLi testing automation script
├── run_xss.sh                # XSS testing automation script
├── requirements.txt          # Python dependencies
├── README.md                 # Basic usage documentation
├── ABOUT.md                  # This comprehensive documentation
├── container/                # Docker containerization
│   ├── Dockerfile           # Kali Linux-based container definition
│   └── run_docker.sh        # Container startup and management script
├── logs/                     # Execution logs and results
│   ├── sqli/                # SQL injection testing logs
│   │   ├── conversations_dump/  # Agent conversation storage
│   │   └── success/         # Successful exploitation logs
│   └── xss/                 # XSS testing logs
│       ├── conversations_dump/  # Agent conversation storage
│       └── success/         # Successful exploitation logs
└── venv/                    # Python virtual environment
    ├── bin/                 # Virtual environment binaries
    ├── include/             # Header files
    ├── lib/                 # Python libraries
    └── pyvenv.cfg           # Virtual environment configuration
```

## Core Components

### 1. SQL Injection Agent (sqliAgent.py)

**Purpose**: Specialized AI agent for detecting and exploiting SQL injection vulnerabilities.

**Key Features**:
- **Multi-technique Testing**: Error-based, Union-based, Boolean-based, and Time-based SQLi
- **Intelligent Payload Generation**: Context-aware SQL injection payloads
- **Response Analysis**: Comprehensive analysis of HTTP responses, timing, and error patterns
- **Data Extraction**: Automated database enumeration and flag extraction
- **Docker Integration**: Secure execution within isolated Kali Linux container

**Core Functions**:
- `execute_python_code()`: Executes Python code within Docker container
- `execute_terminal_command()`: Runs shell commands in containerized environment
- `sqli_agent()`: Factory function for creating configured SQLi agents
- `display_execution_summary()`: Provides detailed metrics and performance data

**Supported AI Models**:
- DeepSeek
- Claude (Anthropic)
- Gemini (Google)
- Azure AI Foundry

**Testing Workflow**:
1. **Baseline Profiling**: Identify parameters and normal application behavior
2. **Payload Spray**: Test multiple SQLi payloads across all parameters
3. **Technique Pivot**: Apply different SQLi techniques in order of effectiveness
4. **Signal Confirmation**: Verify positive results with variant payloads
5. **Data Extraction**: Extract target data using confirmed techniques

### 2. Cross-Site Scripting Agent (xssAgent.py)

**Purpose**: Specialized AI agent for detecting and exploiting XSS vulnerabilities.

**Key Features**:
- **Multi-context Testing**: HTML, JavaScript, attribute, and JSON contexts
- **Payload Diversification**: Script tags, event handlers, SVG, image sources
- **DOM Analysis**: Client-side sink detection and exploitation
- **Browser Integration**: MCP (Model Context Protocol) for browser automation
- **Persistence Detection**: Stored XSS identification and exploitation

**Core Functions**:
- `execute_python_code()`: Python code execution in Docker environment
- `execute_terminal_command()`: Shell command execution
- `xss_agent()`: Factory function for XSS agent configuration
- `display_execution_summary()`: Performance and metrics reporting

**Testing Workflow**:
1. **Baseline Profiling**: Map all input vectors and reflection points
2. **Payload Spray**: Context-aware XSS payload injection
3. **Technique Pivot**: Reflected → Stored → DOM-based XSS
4. **Signal Confirmation**: Verify execution with multiple payloads
5. **Exploitation**: Generate proof-of-concept exploits

### 3. Docker Container Environment

**Base Image**: Kali Linux Rolling
**Container Name**: `kali`

**Installed Tools**:
- **Web Assessment Suite**: Complete Kali web testing toolkit
- **Python Environment**: Python 3 with security libraries
- **Network Tools**: curl, wget, netcat, nmap
- **Analysis Tools**: jq, grep, sed, awk
- **Development Tools**: git, pipx, uv package manager

**Security Features**:
- Isolated execution environment
- No network access to host system
- Containerized tool execution
- Secure code evaluation

### 4. Automation Scripts

#### run_sqli.sh
- **Purpose**: Automated SQLi testing with logging
- **Features**: 
  - URL validation and parsing
  - Automatic log file generation
  - Timestamped output files
  - Color-preserved logging using `script` command

#### run_xss.sh
- **Purpose**: Automated XSS testing with logging
- **Features**:
  - URL processing and validation
  - Structured log file naming
  - Complete session recording
  - Error handling and cleanup

## Technical Architecture

### AI Agent Framework (Agno)

The system is built on the Agno framework, providing:
- **Multi-model Support**: Integration with various AI providers
- **Tool Integration**: Seamless tool calling and execution
- **Memory Management**: Conversation and context persistence
- **Debug Capabilities**: Comprehensive logging and debugging
- **Storage Systems**: JSON-based conversation storage

### Model Context Protocol (MCP)

Integration with MCP enables:
- **Browser Automation**: Playwright-based web interaction
- **Real-time Testing**: Interactive vulnerability assessment
- **Dynamic Analysis**: Client-side behavior observation
- **Session Management**: Stateful testing across requests

### Execution Environment

**Docker Integration**:
- Secure, isolated execution
- Consistent testing environment
- Tool availability and versioning
- Network isolation and security

**Python Execution**:
- Base64 encoding for secure code transfer
- Timeout mechanisms for runaway processes
- Comprehensive error handling
- Output capture and analysis

## Configuration and Setup

### Environment Variables

Required environment variables in `.env` file:
```bash
# AI Model API Keys
DEEPSEEK_API_KEY=your_deepseek_api_key
ANTHROPIC_API_KEY=your_claude_api_key
GOOGLE_API_KEY=your_gemini_api_key
AZURE_API_KEY=your_azure_api_key
AZURE_ENDPOINT=your_azure_endpoint
```

### Dependencies

Core Python packages (requirements.txt):
- `agno`: AI agent framework
- `docker`: Docker SDK for Python
- `python-dotenv`: Environment variable management
- `black`: Code formatting
- `pygments`: Syntax highlighting
- `pydantic`: Data validation
- `mcp`: Model Context Protocol
- `azure-ai-inference`: Azure AI integration
- `aiohttp`: Async HTTP client
- `google-genai`: Google AI integration
- `requests`: HTTP library

## Usage Patterns

### Command Line Interface

**SQL Injection Testing**:
```bash
python sqliAgent.py -u "http://target.com/vulnerable.php?id=1"
python sqliAgent.py --url "https://example.com/login" --container kali
```

**XSS Testing**:
```bash
python xssAgent.py -u "http://target.com/search.php?q=test"
python xssAgent.py --url "https://example.com/form" --container kali
```

**Automated Testing**:
```bash
./run_sqli.sh "http://************:8080/Less-1/"
./run_xss.sh "https://vulnerable-site.com/search"
```

### Programmatic Usage

```python
from sqliAgent import sqli_agent
from agno.models.gemini import Gemini

# Create agent
agent = sqli_agent(Gemini(id="gemini-2.5-pro"))

# Run assessment
result = await agent.arun("Test http://target.com for SQLi")
```

## Logging and Output

### Log Structure

**File Naming Convention**:
```
{timestamp}_{ip_address}_{path_component}.txt
```

**Example**:
```
1754283903_192_168_1_20_http:__************:8080_Less-5_.txt
```

**Log Contents**:
- Target URL and container information
- Agent initialization and configuration
- Tool execution logs with timestamps
- AI model interactions and responses
- Vulnerability discovery process
- Exploitation attempts and results
- Performance metrics and token usage

### Success Indicators

**SQL Injection**:
- Database version extraction
- Table enumeration
- Flag extraction (format: `csk_flag_...`)
- Working curl commands for reproduction

**XSS**:
- Payload reflection confirmation
- JavaScript execution evidence
- Alert box triggers
- Cookie/session theft proof-of-concepts

## Advanced Features

### Multi-Model Support

The framework supports switching between different AI models:
- **DeepSeek**: Cost-effective, high-performance
- **Claude**: Advanced reasoning capabilities
- **Gemini**: Google's latest AI technology
- **Azure AI**: Enterprise-grade AI services

### Intelligent Payload Generation

**Context-Aware Payloads**:
- SQL injection payloads adapted to database type
- XSS payloads tailored to injection context
- Encoding and evasion techniques
- WAF bypass strategies

### Performance Monitoring

**Execution Metrics**:
- Token usage tracking (input/output/total)
- Execution time measurement
- Success rate analysis
- Cost calculation for API usage

### Error Handling and Recovery

**Robust Error Management**:
- Docker connection failures
- Network timeout handling
- AI model API errors
- Container execution issues
- Graceful degradation strategies

## Security Considerations

### Isolation and Safety

**Container Security**:
- No host network access
- Isolated file system
- Limited resource allocation
- Secure tool execution

**Code Execution Safety**:
- Base64 encoding for code transfer
- Timeout mechanisms
- Error containment
- Output sanitization

### Ethical Usage

**Responsible Disclosure**:
- Only test authorized targets
- Follow responsible disclosure practices
- Respect rate limits and resources
- Document findings appropriately

## Integration Points

### External Tools

**MCP Server Integration**:
- Playwright browser automation
- Real-time web interaction
- Dynamic content analysis
- Session state management

**Docker Ecosystem**:
- Kali Linux tool suite
- Custom container builds
- Volume mounting for data persistence
- Network configuration options

### API Integrations

**AI Model APIs**:
- OpenAI-compatible endpoints
- Azure AI services
- Google AI platform
- Anthropic Claude API

## Troubleshooting

### Common Issues

**Docker Problems**:
- Container not running: `docker ps` to check status
- Permission issues: Ensure Docker daemon is accessible
- Image not found: Rebuild with `docker build -t kali-web .`

**AI Model Issues**:
- API key validation
- Rate limit handling
- Model availability
- Network connectivity

**Environment Issues**:
- Virtual environment activation
- Dependency installation
- Environment variable configuration
- Path and permission problems

### Debug Mode

Enable comprehensive debugging:
```python
agent = sqli_agent(model, debug_mode=True, show_tool_calls=True)
```

## System Prompts and AI Instructions

### SQL Injection Agent Prompts

The SQLi agent uses sophisticated system prompts that guide the AI through:

**SQLI_SYSTEM_PROMPT2** (Primary):
- Role definition as autonomous penetration tester
- Toolbox overview with available functions
- Structured workflow for systematic testing
- Rules of engagement for effective testing
- Output format specifications

**Key Workflow Steps**:
1. **Baseline Profiling**: Parameter identification and behavior analysis
2. **Payload Spray**: Multi-payload testing with loop-driven requests
3. **Technique Pivot**: Error-based → Union-based → Boolean-based → Time-based
4. **Signal Confirmation**: Variant payload verification
5. **Data Extraction**: Target data enumeration and extraction

### XSS Agent Prompts

The XSS agent employs specialized prompts for:

**XSS_SYSTEM_PROMPT** (Primary):
- Cross-site scripting vulnerability focus
- Context-aware payload generation
- Multi-technique testing approach
- Browser automation integration
- Proof-of-concept development

**Testing Contexts**:
- HTML injection points
- JavaScript execution contexts
- Attribute-based injections
- JSON/AJAX endpoints
- DOM manipulation sinks

## Data Models and Structures

### Core Data Classes

**CodeResponse**:
```python
@dataclass
class CodeResponse:
    output: str | None
    error: str | None
```

**TerminalResponse**:
```python
@dataclass
class TerminalResponse:
    output: str | None
    error: str | None
    return_code: int
    execution_time: float
```

**FlagReport** (Pydantic Model):
```python
class FlagReport(BaseModel):
    isvulnerable: bool
    description: str | None = None
    curl_command: str | None = None
    flag: str | None = None
```

## Execution Flow

### Agent Initialization

1. **Environment Setup**: Load environment variables and API keys
2. **Model Selection**: Choose and configure AI model
3. **Tool Registration**: Register execution functions and MCP tools
4. **Container Verification**: Ensure Docker container is running
5. **MCP Connection**: Establish browser automation connection

### Testing Execution

1. **Target Analysis**: Parse and validate target URL
2. **Agent Creation**: Initialize specialized agent with system prompts
3. **Task Execution**: Run autonomous testing workflow
4. **Result Processing**: Analyze findings and generate reports
5. **Cleanup**: Close connections and save logs

### Output Generation

1. **Log Creation**: Timestamped log files with complete session data
2. **Metrics Collection**: Token usage, execution time, success rates
3. **Evidence Compilation**: Screenshots, payloads, reproduction steps
4. **Report Formatting**: Structured output with vulnerability details

## Performance Characteristics

### Token Usage Patterns

**SQL Injection Testing**:
- Average input tokens: 15,000-25,000 per session
- Average output tokens: 8,000-15,000 per session
- Complex targets may require 50,000+ total tokens

**XSS Testing**:
- Average input tokens: 12,000-20,000 per session
- Average output tokens: 6,000-12,000 per session
- Browser automation adds 20-30% token overhead

### Execution Times

**Typical Session Duration**:
- Simple targets: 2-5 minutes
- Complex applications: 10-20 minutes
- Comprehensive assessments: 30-60 minutes

**Factors Affecting Performance**:
- Target application complexity
- Network latency and response times
- AI model processing speed
- Container resource allocation

## Cost Analysis

### API Cost Estimation

**Azure OpenAI Pricing** (as referenced in memories):
- Input tokens: $2.50 per 1M tokens
- Output tokens: $10.00 per 1M tokens

**Typical Session Costs**:
- Simple SQLi test: $0.15-0.30
- Complex SQLi assessment: $0.50-1.00
- XSS testing: $0.10-0.25
- Comprehensive assessment: $1.00-2.50

## Future Enhancements

### Planned Features

**Additional Vulnerability Types**:
- Command injection detection
- File upload vulnerabilities
- Authentication bypass
- Authorization flaws
- Server-side request forgery (SSRF)
- XML external entity (XXE) attacks

**Enhanced Reporting**:
- JSON/XML output formats
- Integration with security tools (Burp Suite, OWASP ZAP)
- Automated report generation
- Vulnerability scoring (CVSS)
- Risk assessment matrices

**Performance Improvements**:
- Parallel testing capabilities
- Intelligent caching mechanisms
- Optimized payload selection
- Reduced API calls through batching
- Local model support

**Advanced AI Features**:
- Multi-agent collaboration
- Adaptive learning from previous tests
- Custom payload generation
- WAF evasion techniques
- Zero-day discovery capabilities

## Contributing

### Development Guidelines

**Code Standards**:
- Follow PEP 8 style guidelines
- Use type hints for function signatures
- Implement comprehensive error handling
- Add docstrings for all functions
- Maintain backward compatibility

**Testing Requirements**:
- Unit tests for core functions
- Integration tests for agent workflows
- Docker environment validation
- AI model compatibility testing
- Performance benchmarking

### Extension Points

**Custom Agents**:
- Implement new vulnerability types
- Add specialized testing techniques
- Integrate additional AI models
- Extend reporting capabilities
- Create domain-specific agents

**Tool Integration**:
- Add new security tools
- Implement custom analyzers
- Extend container capabilities
- Integrate with CI/CD pipelines
- Support additional output formats

## Conclusion

The `Inv_agents` directory represents a sophisticated, AI-powered vulnerability assessment framework that combines the intelligence of modern language models with the precision of specialized security tools. It provides a comprehensive solution for automated penetration testing, offering both ease of use for security professionals and extensibility for advanced users.

The framework's modular design, robust error handling, and comprehensive logging make it suitable for both educational purposes and professional security assessments. Its containerized execution environment ensures safety and consistency, while the multi-model AI support provides flexibility and adaptability to different testing scenarios.

This system demonstrates the potential of AI-assisted security testing, providing a foundation for the future of automated vulnerability assessment and exploitation. The combination of intelligent agents, secure execution environments, and comprehensive reporting creates a powerful platform for advancing cybersecurity research and practice.

**Key Strengths**:
- Autonomous operation with minimal human intervention
- Multi-model AI support for diverse testing approaches
- Secure, isolated execution environment
- Comprehensive logging and reporting
- Extensible architecture for future enhancements
- Professional-grade tooling and methodologies

**Use Cases**:
- Penetration testing and security assessments
- Vulnerability research and discovery
- Security training and education
- Automated security testing in CI/CD pipelines
- Bug bounty hunting and responsible disclosure
- Academic research in AI-assisted security testing

This framework represents a significant advancement in automated security testing, bridging the gap between traditional tools and intelligent, adaptive testing methodologies.
