{"manifest_version": 3, "name": "Playwright <PERSON><PERSON>", "version": "1.0.0", "description": "Share browser tabs with Playwright MCP server through CDP bridge", "permissions": ["debugger", "activeTab", "tabs", "storage"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "lib/background.js", "type": "module"}, "action": {"default_title": "Share tab with <PERSON><PERSON> <PERSON><PERSON>", "default_popup": "popup.html", "default_icon": {"16": "icons/icon-16.png", "32": "icons/icon-32.png", "48": "icons/icon-48.png", "128": "icons/icon-128.png"}}, "icons": {"16": "icons/icon-16.png", "32": "icons/icon-32.png", "48": "icons/icon-48.png", "128": "icons/icon-128.png"}}