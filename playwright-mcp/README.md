# Playwright MCP Server - Development Guide

A Model Context Protocol (MCP) server that provides browser automation capabilities using [<PERSON>wright](https://playwright.dev). This enables LLMs to interact with web pages through structured accessibility snapshots.

## Quick Start

### Prerequisites
- Node.js 18+ 
- Any MCP client (VS Code, <PERSON>ursor, <PERSON>, etc.)

### Setup & Run

```bash
# 1. Install dependencies
npm install

# 2. Build the project
npm run build

# 3. Run the server
node cli.js

# Or with options
node cli.js --headless --browser chrome --port 8931
```

## Development Workflow

### Building & Watching
```bash
# Build once
npm run build

# Watch for changes (recommended for development)
npm run watch

# Clean build artifacts
npm run clean
```

### Running Locally
```bash
# Default: Stdio transport, headed Chrome
node cli.js

# HTTP/SSE transport on port 8931
node cli.js --port 8931

# Headless mode
node cli.js --headless

# Different browser
node cli.js --browser firefox

# Browser agent mode (experimental)
node lib/browserServer.js --port 9224
```

### Testing
```bash
# Run all tests
npm test

# Chrome tests only
npm run ctest

# Firefox tests only
npm run ftest

# WebKit tests only
npm run wtest

# Extension tests
npm run etest
```

## Project Structure

```
src/
├── program.ts          # CLI entry point and argument parsing
├── server.ts           # Main MCP server implementation
├── context.ts          # Browser context management
├── transport.ts        # MCP transport layers (stdio, SSE, HTTP)
├── browserServer.ts    # Browser agent server
├── config.ts           # Configuration handling
├── tools/              # MCP tool implementations
│   ├── browser.ts      # Browser control tools
│   ├── navigation.ts   # Page navigation tools
│   └── interaction.ts  # Element interaction tools
└── resources/          # Static resources

lib/                    # Compiled JavaScript output
tests/                  # Test files
extension/              # Chrome extension (experimental)
```

## Adding New Features

### 1. Add a New MCP Tool

1. **Create tool file** in `src/tools/`:
```typescript
// src/tools/myFeature.ts
import { tool } from '@modelcontextprotocol/sdk/server/index.js';

export const myNewTool = tool({
  name: 'my_new_tool',
  description: 'Does something useful',
  inputSchema: {
    type: 'object',
    properties: {
      param: { type: 'string', description: 'Input parameter' }
    },
    required: ['param']
  }
}, async (request, context) => {
  // Implementation here
  return {
    content: [{ type: 'text', text: 'Result' }]
  };
});
```

2. **Register tool** in `src/tools.ts`:
```typescript
import { myNewTool } from './tools/myFeature.js';

// Add to tools array
this._server.setRequestHandler(ListToolsRequestSchema, async () => ({
  tools: [
    // ... existing tools
    myNewTool.definition,
  ]
}));
```

3. **Add tool handler** in `src/tools.ts`:
```typescript
this._server.setRequestHandler(CallToolRequestSchema, async (request) => {
  switch (request.params.name) {
    // ... existing cases
    case 'my_new_tool':
      return await myNewTool.handler(request, this._context);
  }
});
```

### 2. Add Configuration Options

1. **Update config types** in `config.d.ts`:
```typescript
export interface Config {
  // ... existing options
  myNewOption?: boolean;
}
```

2. **Add CLI option** in `src/program.ts`:
```typescript
program
  .option('--my-new-option', 'description of new option')
```

3. **Handle in config** in `src/config.ts`:
```typescript
const defaultConfig = {
  // ... existing defaults
  myNewOption: false,
};
```

### 3. Add Tests

```typescript
// tests/myFeature.spec.ts
import { test, expect } from './fixtures.js';

test('my new feature works', async ({ client }) => {
  const result = await client.callTool('my_new_tool', { param: 'test' });
  expect(result.content[0].text).toBe('expected result');
});
```

## MCP Client Configuration

### Stdio Transport (Default)
```json
{
  "mcpServers": {
    "playwright": {
      "command": "node",
      "args": ["/path/to/playwright-mcp/cli.js"],
      "cwd": "/path/to/playwright-mcp"
    }
  }
}
```

### HTTP/SSE Transport
```json
{
  "mcpServers": {
    "playwright": {
      "url": "http://localhost:8931/sse"
    }
  }
}
```

## Common Configuration Options

```bash
# Browser selection
--browser chrome|firefox|webkit|msedge

# Display mode
--headless                    # Run headless
--device "iPhone 15"          # Emulate device

# Network control
--allowed-origins "https://example.com"
--blocked-origins "https://ads.com"
--proxy-server "http://proxy:3128"

# Output and debugging
--output-dir /path/to/output
--save-trace                  # Save Playwright traces
--vision                      # Use screenshots instead of accessibility tree

# Server options
--port 8931                   # Enable HTTP transport
--host 0.0.0.0               # Bind to all interfaces
```

## Debugging

```bash
# Enable debug logging
DEBUG=pw:mcp:* node cli.js

# View Playwright traces (if --save-trace enabled)
npx playwright show-trace /path/to/trace.json

# Test specific browser
npm test -- --project=firefox
```

## Using with Agentic Frameworks

### Agno Framework

You can integrate the Playwright MCP server with [Agno](https://docs.agno.com/) for building AI agents with browser automation capabilities.

#### Setup with Agno

1. **Start the local server** with HTTP transport:
```bash
node cli.js --port 8931
```

2. **Install Agno dependencies**:
```bash
pip install agno mcp-sdk
```

3. **Create an Agno agent** with Playwright MCP tools:

```python
import asyncio
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.tools.mcp import MCPTools, StreamableHTTPClientParams

async def run_playwright_agent(task: str):
    # Configure MCP server connection
    server_params = StreamableHTTPClientParams(
        url="http://localhost:8931/mcp",  # Use /mcp endpoint for streamable HTTP
        timeout_seconds=30
    )
    
    # Create agent with Playwright MCP tools
    async with MCPTools(
        server_params=server_params, 
        transport="streamable-http"
    ) as mcp:
        agent = Agent(
            model=OpenAIChat(id="gpt-4o"),
            tools=[mcp],
            markdown=True,
        )
        
        await agent.aprint_response(message=task, stream=True)

# Example usage
if __name__ == "__main__":
    asyncio.run(run_playwright_agent(
        "Navigate to example.com, take a screenshot, and find all the links on the page"
    ))
```

#### Advanced Agno Integration

For more complex workflows, you can combine Playwright MCP with other tools:

```python
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.tools.mcp import MCPTools
from agno.tools.file import WriteFile
from agno.memory import AgentMemory

async def web_testing_agent():
    server_params = StreamableHTTPClientParams(url="http://localhost:8931/mcp")
    
    async with MCPTools(server_params=server_params, transport="streamable-http") as mcp:
        agent = Agent(
            model=OpenAIChat(id="gpt-4o"),
            tools=[mcp, WriteFile()],
            memory=AgentMemory(),
            markdown=True,
            show_tool_calls=True
        )
        
        # Agent can now browse websites, generate tests, and save them to files
        await agent.aprint_response(
            message="Go to https://example.com, explore the site structure, "
                   "and generate comprehensive Playwright tests for the main user flows"
        )

asyncio.run(web_testing_agent())
```

### Other Agentic Frameworks

#### LangChain Integration

```python
from langchain.agents import initialize_agent
from langchain.tools import Tool
import requests

# Create a custom tool that calls your MCP server
def create_playwright_tool():
    def run_playwright_command(command: str) -> str:
        # Call your MCP server via HTTP
        response = requests.post(
            "http://localhost:8931/mcp",
            json={"method": "tools/call", "params": {"name": command}}
        )
        return response.json()
    
    return Tool(
        name="playwright",
        description="Browser automation with Playwright",
        func=run_playwright_command
    )
```

#### AutoGen Integration

```python
import autogen
from autogen import AssistantAgent, UserProxyAgent

# Configure agent with MCP server access
config_list = [{"model": "gpt-4o", "api_key": "your-key"}]

assistant = AssistantAgent(
    name="playwright_assistant",
    llm_config={"config_list": config_list},
    system_message="You are a web automation expert using Playwright MCP server at http://localhost:8931"
)

user_proxy = UserProxyAgent(
    name="user_proxy",
    human_input_mode="NEVER",
    code_execution_config={"work_dir": "web_automation"}
)
```

### Configuration Tips

- **Headless vs Headed**: Use `--headless` for production/CI, headed mode for development and debugging
- **Port Configuration**: Default HTTP transport uses port 8931, but you can customize with `--port`
- **Browser Selection**: Use `--browser firefox` or `--browser webkit` for cross-browser testing
- **Network Controls**: Use `--allowed-origins` and `--blocked-origins` for security in agent environments
- **Tracing**: Enable `--save-trace` to debug agent interactions with the browser

### Security Considerations

When using with agentic frameworks:

1. **Restrict Origins**: Use `--allowed-origins` to limit which websites the agent can access
2. **Isolated Mode**: Use `--isolated` to prevent persistent browser state between sessions  
3. **Sandbox**: Use `--no-sandbox` only when necessary (containers/CI)
4. **Output Directory**: Set `--output-dir` to a secure location for generated files

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes and add tests
4. Run `npm run lint` and `npm test`
5. Submit a pull request

## License

Apache-2.0 - see LICENSE file for details.
