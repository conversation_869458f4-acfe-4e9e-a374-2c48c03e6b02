/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { z } from 'zod';
import { defineTool } from './tool.js';

import type * as playwright from 'playwright';

const requests = defineTool({
  capability: 'core',

  schema: {
    name: 'browser_network_requests',
    title: 'List network requests',
    description: 'Returns all network requests since loading the page with numeric IDs',
    inputSchema: z.object({}),
    type: 'readOnly',
  },

  handle: async context => {
    const requests = context.currentTabOrDie().requests();
    const requestEntries: string[] = [];
    
    // Clear previous cache and assign new IDs
    context.clearRequestsCache();
    
    [...requests.entries()].forEach(([request, response]) => {
      const id = context.storeRequestWithId(request, response);
      requestEntries.push(renderRequestWithId(id, request, response));
    });
    
    const log = requestEntries.join('\n');
    return {
      code: [`// <internal code to list network requests with IDs>`],
      action: async () => {
        return {
          content: [{ type: 'text', text: log }]
        };
      },
      captureSnapshot: false,
      waitForNetwork: false,
    };
  },
});

const requestDetails = defineTool({
  capability: 'core',

  schema: {
    name: 'browser_network_request_details',
    title: 'Get network request details',
    description: 'Returns raw request and response data for a specific request ID',
    inputSchema: z.object({
      id: z.number().describe('The numeric ID of the request to get details for'),
    }),
    type: 'readOnly',
  },

  handle: async (context, params) => {
    const requestData = context.getRequestById(params.id);
    
    if (!requestData) {
      return {
        code: [`// <request ID ${params.id} not found>`],
        action: async () => {
          return {
            content: [{ 
              type: 'text', 
              text: `Error: Request with ID ${params.id} not found. Use browser_network_requests to list available requests.` 
            }]
          };
        },
        captureSnapshot: false,
        waitForNetwork: false,
      };
    }

    const { request, response } = requestData;
    
    return {
      code: [`// <internal code to get request ${params.id} details>`],
      action: async () => {
        const rawRequest = await buildRawRequest(request);
        const rawResponse = response ? await buildRawResponse(response) : null;
        
        const result = {
          raw_request: rawRequest,
          raw_response: rawResponse
        };
        
        return {
          content: [{ 
            type: 'text', 
            text: JSON.stringify(result, null, 2) 
          }]
        };
      },
      captureSnapshot: false,
      waitForNetwork: false,
    };
  },
});

function renderRequest(request: playwright.Request, response: playwright.Response | null) {
  const result: string[] = [];
  result.push(`[${request.method().toUpperCase()}] ${request.url()}`);
  if (response)
    result.push(`=> [${response.status()}] ${response.statusText()}`);
  return result.join(' ');
}

function renderRequestWithId(id: number, request: playwright.Request, response: playwright.Response | null) {
  const result: string[] = [];
  result.push(`[ID: ${id}] [${request.method().toUpperCase()}] ${request.url()}`);
  if (response)
    result.push(`=> [${response.status()}] ${response.statusText()}`);
  return result.join(' ');
}

async function buildRawRequest(request: playwright.Request): Promise<string> {
  const url = new URL(request.url());
  const headers = request.headers();
  const method = request.method();
  const postData = request.postData();
  
  // Build HTTP request line
  let rawRequest = `${method.toUpperCase()} ${url.pathname}${url.search} HTTP/1.1\r\n`;
  
  // Add Host header if not present
  if (!headers['host'] && !headers['Host']) {
    rawRequest += `Host: ${url.host}\r\n`;
  }
  
  // Add headers
  for (const [name, value] of Object.entries(headers)) {
    rawRequest += `${name}: ${value}\r\n`;
  }
  
  // Add body if present
  if (postData) {
    rawRequest += `\r\n${postData}`;
  } else {
    rawRequest += '\r\n';
  }
  
  return rawRequest;
}

async function buildRawResponse(response: playwright.Response): Promise<string> {
  const status = response.status();
  const statusText = response.statusText();
  const headers = response.headers();
  
  // Build HTTP status line
  let rawResponse = `HTTP/1.1 ${status} ${statusText}\r\n`;
  
  // Add headers
  for (const [name, value] of Object.entries(headers)) {
    rawResponse += `${name}: ${value}\r\n`;
  }
  
  // Try to get response body (be careful with large responses)
  try {
    const body = await response.body();
    if (body && body.length > 0) {
      rawResponse += `\r\n${body.toString()}`;
    } else {
      rawResponse += '\r\n';
    }
  } catch (error) {
    // If we can't get the body, just add empty line
    rawResponse += '\r\n';
  }
  
  return rawResponse;
}

export default [
  requests,
  requestDetails,
];
